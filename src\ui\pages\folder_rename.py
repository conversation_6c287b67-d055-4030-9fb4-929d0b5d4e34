# ui/pages/folder_rename.py
import os
import random
import re
import string
import time
from pathlib import Path

from PySide6.QtCore import Signal, QDateTime, Qt, QTimer
from PySide6.QtGui import QDesktopServices
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
                               QTableWidgetItem, QHeaderView, QTabWidget, QFormLayout, QRadioButton,
                               QSpinBox, QTextEdit, QComboBox, QCheckBox, QLineEdit, QGroupBox,
                               QLabel, QButtonGroup, QFileDialog, QStyledItemDelegate, QMessageBox)

from core.utils import StringUtils, HoverTableWidget
from core.license_limiter import get_license_limiter


class FilenameItemDelegate(QStyledItemDelegate):
    """自定义表格委托，用于文件夹名编辑"""
    def setEditorData(self, editor, index):
        """初始化编辑器"""
        original = index.model().data(index.model().index(index.row(), 0), Qt.DisplayRole)
        # 文件夹无需处理扩展名
        editor.setText(original)

    def setModelData(self, editor, model, index):
        """提交编辑"""
        original = model.data(model.index(index.row(), 0), Qt.DisplayRole)
        # 直接使用编辑后的完整名称
        model.setData(index, editor.text(), Qt.EditRole)


class FolderRenamePage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_function = 0  # 当前激活的功能标签索引
        self.all_files_list = []   # 存储所有文件夹路径
        self.setup_ui()
        self.connect_signals()            # 连接信号与槽
        self.setup_conditional_visibility()  # 设置控件可见性
        self.rename_history = []          # 存储批次操作历史
        self.setAcceptDrops(True)         #实现拖拽

        # 设置表格委托
        self.table.setItemDelegateForColumn(2, FilenameItemDelegate())

        # 初始化许可证限制管理器
        self.license_limiter = get_license_limiter()
    def setup_ui(self):
        main_layout = QHBoxLayout(self)
        # 参考Function List与RightPanel的间距设置，设置LeftPanel与RightPanel间距为2px
        main_layout.setSpacing(2)
        # 设置右边距为2px，将右侧面板向右移动
        main_layout.setContentsMargins(0, 0, 2, 0)

        # 左侧文件列表区
        left_panel = QWidget()
        left_panel.setObjectName("LeftPanel")  # 设置对象名，使CSS样式生效
        left_layout = QVBoxLayout(left_panel)

        # 文件操作按钮

        btn_layout = QHBoxLayout()
        self.btn_clear = QPushButton("清空列表", objectName="dangerBtn")

        self.btn_add_folder = QPushButton("添加文件夹", objectName="primaryBtn")
        self.title_label = QLabel(f"文件夹重命名", objectName="functionTitle")
        btn_layout.addWidget(self.title_label)

        btn_layout.addWidget(self.btn_clear)

        btn_layout.addWidget(self.btn_add_folder)

        # 文件表格
        self.table = HoverTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(["文件夹名", "原路径", "新文件夹名", "状态", "排序", "操作"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.verticalHeader().setDefaultSectionSize(50)  # 设置默认行高
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Fixed)  # 设置状态列为固定宽度
        self.table.setColumnWidth(3, 70)  # 设置具体像素值
        self.table.setShowGrid(False)  # 移除网格线
        self.table.verticalHeader().setVisible(False)

        self.table.setSortingEnabled(True)
        self.table.setSelectionBehavior(HoverTableWidget.SelectRows)

        left_layout.addLayout(btn_layout)
        left_layout.addWidget(self.table)
        self.table.setAlternatingRowColors(True)  # 启用交替行颜色

        # 在表格下方添加状态信息标签
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.status_label.setObjectName("statusLabel")
        left_layout.addWidget(self.status_label)  # 添加到左侧面板底部


        # 右侧功能设置区
        right_panel = QWidget()
        right_panel.setObjectName("rightPanel")  # 设置对象名，使CSS样式生效
        right_panel.setMinimumWidth(320)  # 新增宽度限制
        right_layout = QVBoxLayout(right_panel)
        right_layout.setAlignment(Qt.AlignLeft)  # 整体左上对齐
        # 功能标签页容器
        self.tab_widget = QTabWidget()
        self.setup_basic_tab()    # 新命名
        self.setup_add_tab()      # 增加
        self.setup_modify_tab()   # 修改
        self.setup_delete_tab()   # 删除
        self.setup_custom_tab()   # 自定义

        right_layout.addWidget(self.tab_widget, stretch=1)
        # 操作按钮行
        action_layout = QHBoxLayout()
        self.btn_undo = QPushButton("撤回", objectName="warningBtn")
        self.btn_start = QPushButton("开始重命名", objectName="successBtn")
        action_layout.addWidget(self.btn_undo)
        action_layout.addWidget(self.btn_start)
        #action_layout.addStretch()
        right_layout.addLayout(action_layout)

        main_layout.addWidget(left_panel, stretch=3)
        main_layout.addWidget(right_panel, stretch=1)

    def setup_basic_tab(self):
        """新命名标签页（原有功能）"""
        tab = QWidget()

        # 主布局使用垂直布局，设置统一边距和间距
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(12, 12, 12, 12)  # 外边界
        main_layout.setSpacing(15)  # 部件间距

        # ==================== 文件夹名设置区块 ====================
        name_section = QVBoxLayout()
        name_section.setContentsMargins(0, 0, 0, 0)
        name_section.setSpacing(8)

        # 标题标签
        self.title_label = QLabel("文件夹名设置", objectName="basicTitle")
        self.title_label.setFixedHeight(20)
        name_section.addWidget(self.title_label)

        # 文本编辑框
        self.basic_rule_edit = QTextEdit()
        self.basic_rule_edit.setPlaceholderText("请输入新文件名")
        self.basic_rule_edit.setFixedHeight(80)
        name_section.addWidget(self.basic_rule_edit)

        main_layout.addLayout(name_section)

        # ==================== 编号位置区块 ====================
        pos_section = QVBoxLayout()
        pos_section.setContentsMargins(0, 0, 0, 0)
        pos_section.setSpacing(6)

        # 位置标题
        pos_label = QLabel("编号位置", objectName="basicTitle")
        pos_section.addWidget(pos_label)

        # 单选框容器
        radio_widget = QWidget()
        radio_layout = QHBoxLayout(radio_widget)
        radio_layout.setContentsMargins(0, 0, 0, 0)
        radio_layout.setSpacing(20)  # 单选项间距

        # 创建单选按钮组
        self.basic_position_group = QButtonGroup(self)
        self.basic_prefix_radio = QRadioButton("前缀", checked=True)
        self.basic_suffix_radio = QRadioButton("后缀")
        self.basic_custom_radio = QRadioButton("自定义")

        self.basic_position_group.addButton(self.basic_prefix_radio)
        self.basic_position_group.addButton(self.basic_suffix_radio)
        self.basic_position_group.addButton(self.basic_custom_radio)

        radio_layout.addWidget(self.basic_prefix_radio)
        radio_layout.addWidget(self.basic_suffix_radio)
        radio_layout.addWidget(self.basic_custom_radio)

        pos_section.addWidget(radio_widget)

        # 自定义位置控件容器
        self.basic_custom_pos_container = QWidget()
        basic_custom_pos_layout = QHBoxLayout(self.basic_custom_pos_container)
        basic_custom_pos_layout.setContentsMargins(0, 0, 0, 0)

        # 添加提示标签和SpinBox
        basic_custom_pos_layout.addWidget(QLabel("插入位置:"))
        self.basic_custom_position = QSpinBox()
        self.basic_custom_position.setMinimum(0)
        self.basic_custom_position.setMaximum(999)
        self.basic_custom_position.setValue(0)
        self.basic_custom_position.setToolTip("指定要插入的位置（从0开始计数）")
        basic_custom_pos_layout.addWidget(self.basic_custom_position)
        basic_custom_pos_layout.addStretch()

        # 初始状态隐藏
        self.basic_custom_pos_container.setVisible(False)

        # 连接单选按钮信号到显示/隐藏槽函数
        self.basic_custom_radio.toggled.connect(self.basic_custom_pos_container.setVisible)

        pos_section.addWidget(self.basic_custom_pos_container)
        main_layout.addLayout(pos_section)

        # ==================== 序列设置区块 ====================
        # 创建表单布局容器
        seq_form_widget = QWidget()
        seq_form_layout = QFormLayout(seq_form_widget)
        seq_form_layout.setContentsMargins(0, 0, 0, 0)
        seq_form_layout.setHorizontalSpacing(15)  # 标签与控件间距
        seq_form_layout.setVerticalSpacing(8)
        self.setup_sequence_settings(seq_form_layout, prefix="basic_")

        main_layout.addWidget(seq_form_widget)

        # ==================== 自动解决选项 ====================
        self.auto_resolve_check = QCheckBox("自动解决命名重复冲突")
        self.auto_resolve_check.setChecked(True)
        main_layout.addWidget(self.auto_resolve_check)

        # 添加弹性空间使内容顶部对齐
        main_layout.addStretch()

        self.tab_widget.addTab(tab, "新命名")

    def setup_add_tab(self):  # pylint: disable=duplicate-code
        """增加功能标签页（参考文件重命名页面结构）"""
        tab = QWidget()

        # 主布局使用垂直布局，并设置统一的边距和间距
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(8, 8, 8, 8)  # 整体外边界从12减少到8
        main_layout.setSpacing(10)  # 各部件之间的间距从15减少到10

        # ==================== 插入位置区块 ====================
        pos_section = QVBoxLayout()
        pos_section.setContentsMargins(0, 0, 0, 0)
        pos_section.setSpacing(6)

        # 位置标题
        pos_label = QLabel("位置", objectName="basicTitle")
        pos_section.addWidget(pos_label)

        # 单选框容器
        radio_widget = QWidget()
        radio_layout = QHBoxLayout(radio_widget)
        radio_layout.setContentsMargins(0, 0, 0, 0)
        radio_layout.setSpacing(20)  # 调整单选项间距与参考代码一致

        # 创建单选按钮组
        self.add_position_group = QButtonGroup(self)
        self.add_prefix_radio = QRadioButton("前缀", checked=True)
        self.add_suffix_radio = QRadioButton("后缀")
        self.add_custom_radio = QRadioButton("自定义")

        self.add_position_group.addButton(self.add_prefix_radio)
        self.add_position_group.addButton(self.add_suffix_radio)
        self.add_position_group.addButton(self.add_custom_radio)

        radio_layout.addWidget(self.add_prefix_radio)
        radio_layout.addWidget(self.add_suffix_radio)
        radio_layout.addWidget(self.add_custom_radio)

        pos_section.addWidget(radio_widget)

        # 自定义位置控件容器
        self.add_custom_pos_container = QWidget()
        add_custom_pos_layout = QHBoxLayout(self.add_custom_pos_container)
        add_custom_pos_layout.setContentsMargins(0, 0, 0, 0)

        # 添加提示标签和SpinBox
        add_custom_pos_layout.addWidget(QLabel("插入位置:"))
        self.add_custom_position = QSpinBox()
        self.add_custom_position.setMinimum(0)
        self.add_custom_position.setMaximum(999)
        self.add_custom_position.setValue(1)
        self.add_custom_position.setToolTip("指定要插入的位置（从0开始计数）")
        add_custom_pos_layout.addWidget(self.add_custom_position)
        add_custom_pos_layout.addStretch()

        # 初始状态隐藏
        self.add_custom_pos_container.setVisible(False)

        # 连接单选按钮信号到显示/隐藏槽函数
        self.add_custom_radio.toggled.connect(self.add_custom_pos_container.setVisible)

        pos_section.addWidget(self.add_custom_pos_container)
        main_layout.addLayout(pos_section)

        # ==================== 插入名称区块 ====================
        name_section = QVBoxLayout()
        name_section.setContentsMargins(0, 0, 0, 0)
        name_section.setSpacing(8)

        # 标题标签
        self.title_label = QLabel("插入名称", objectName="basicTitle")
        self.title_label.setFixedHeight(20)
        name_section.addWidget(self.title_label)

        # 文本编辑框
        self.add_rule_edit = QTextEdit()
        self.add_rule_edit.setPlaceholderText("请输入要添加的文件夹名")
        self.add_rule_edit.setFixedHeight(80)
        name_section.addWidget(self.add_rule_edit)

        main_layout.addLayout(name_section)



        # ==================== 编号设置复选框 ====================
        # 创建带蓝色标识的容器
        sequence_section = QVBoxLayout()
        sequence_section.setContentsMargins(0, 0, 0, 0)
        sequence_section.setSpacing(6)

        # 创建带蓝色标识的标题容器
        sequence_title_container = QWidget()
        sequence_title_container.setObjectName("basicTitle")  # 使用与其他标题相同的样式
        sequence_title_layout = QHBoxLayout(sequence_title_container)
        sequence_title_layout.setContentsMargins(8, 0, 0, 0)  # 左边距8px，与标题样式一致
        sequence_title_layout.setSpacing(0)

        self.add_sequence_enabled = QCheckBox("编号设置")
        self.add_sequence_enabled.setChecked(True)  # 默认启用
        self.add_sequence_enabled.setToolTip("选中时启用序号位置和序号类型设置，否则不添加序号")
        # self.add_sequence_enabled.setStyleSheet("QCheckBox { margin: 0px; padding: 0px; font-size: 14px; font-weight: 600; color: #2d2d2d; }")  # 已恢复为系统默认样式
        self.add_sequence_enabled.setFocusPolicy(Qt.StrongFocus)  # 确保可以接收焦点

        sequence_title_layout.addWidget(self.add_sequence_enabled)
        sequence_title_layout.addStretch()  # 添加弹性空间

        sequence_section.addWidget(sequence_title_container)
        main_layout.addLayout(sequence_section)

        # ==================== 序列设置区块 ====================
        self.add_seq_settings_container = QWidget()  # 创建容器以便整体控制可见性
        seq_settings_layout = QVBoxLayout(self.add_seq_settings_container)
        seq_settings_layout.setContentsMargins(0, 0, 0, 0)
        seq_settings_layout.setSpacing(15)

        # 添加序列号位置控制
        seq_pos_widget = QWidget()
        seq_pos_layout = QVBoxLayout(seq_pos_widget)
        seq_pos_layout.setContentsMargins(0, 0, 0, 0)
        seq_pos_layout.setSpacing(6)

        # 序列号位置单选按钮组
        seq_radio_widget = QWidget()
        seq_radio_layout = QHBoxLayout(seq_radio_widget)
        seq_radio_layout.setContentsMargins(0, 0, 0, 0)
        seq_radio_layout.setSpacing(20)

        self.add_seq_position_group = QButtonGroup(self)
        self.add_seq_prefix_radio = QRadioButton("前缀", checked=True)
        self.add_seq_suffix_radio = QRadioButton("后缀")
        self.add_seq_custom_radio = QRadioButton("自定义")

        self.add_seq_position_group.addButton(self.add_seq_prefix_radio)
        self.add_seq_position_group.addButton(self.add_seq_suffix_radio)
        self.add_seq_position_group.addButton(self.add_seq_custom_radio)

        seq_radio_layout.addWidget(self.add_seq_prefix_radio)
        seq_radio_layout.addWidget(self.add_seq_suffix_radio)
        seq_radio_layout.addWidget(self.add_seq_custom_radio)

        seq_pos_layout.addWidget(seq_radio_widget)

        # 序列号自定义位置控件
        self.add_seq_custom_pos_container = QWidget()
        seq_custom_pos_layout = QHBoxLayout(self.add_seq_custom_pos_container)
        seq_custom_pos_layout.setContentsMargins(0, 0, 0, 0)

        seq_custom_pos_layout.addWidget(QLabel("序列号位置:"))
        self.add_seq_custom_position = QSpinBox()
        self.add_seq_custom_position.setMinimum(0)
        self.add_seq_custom_position.setMaximum(999)
        self.add_seq_custom_position.setValue(0)
        self.add_seq_custom_position.setToolTip("指定序列号在文本中的位置（从0开始计数）")
        seq_custom_pos_layout.addWidget(self.add_seq_custom_position)
        seq_custom_pos_layout.addStretch()

        # 初始状态隐藏
        self.add_seq_custom_pos_container.setVisible(False)

        # 连接单选按钮信号到显示/隐藏槽函数
        self.add_seq_custom_radio.toggled.connect(self.add_seq_custom_pos_container.setVisible)

        seq_pos_layout.addWidget(self.add_seq_custom_pos_container)
        seq_settings_layout.addWidget(seq_pos_widget)

        # 创建表单布局容器用于数据类型设置
        seq_form_widget = QWidget()
        seq_form_layout = QFormLayout(seq_form_widget)
        seq_form_layout.setContentsMargins(0, 0, 0, 0)
        seq_form_layout.setHorizontalSpacing(15)  # 标签与控件间距
        seq_form_layout.setVerticalSpacing(8)
        self.setup_sequence_settings(seq_form_layout, prefix="add_")

        seq_settings_layout.addWidget(seq_form_widget)
        main_layout.addWidget(self.add_seq_settings_container)

        # ==================== 自动解决选项 ====================
        self.auto_resolve_check_add = QCheckBox("自动解决命名重复冲突")
        self.auto_resolve_check_add.setChecked(True)
        main_layout.addWidget(self.auto_resolve_check_add)

        # 添加弹性空间使内容顶部对齐
        main_layout.addStretch()

        # 设置初始状态和连接信号
        self.add_sequence_enabled.setChecked(True)  # 确保默认选中
        self.add_seq_settings_container.setEnabled(True)  # 确保默认启用
        self.add_sequence_enabled.toggled.connect(self.add_seq_settings_container.setEnabled)

        self.tab_widget.addTab(tab, "增加")

    def setup_modify_tab(self):
        """修改功能标签页"""
        tab = QWidget()

        # 主布局使用垂直布局，设置统一边距和间距
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(12, 12, 12, 12)  # 外边界
        main_layout.setSpacing(15)  # 部件间距

        # ==================== 查找替换区块 ====================
        find_replace_section = QVBoxLayout()
        find_replace_section.setContentsMargins(0, 0, 0, 0)
        find_replace_section.setSpacing(8)

        # 标题标签
        find_title = QLabel("查找替换设置", objectName="basicTitle")
        find_title.setFixedHeight(20)
        find_replace_section.addWidget(find_title)

        # 表单布局容器
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setHorizontalSpacing(15)  # 标签与控件间距
        form_layout.setVerticalSpacing(10)

        self.modify_find_edit = QLineEdit()
        self.modify_replace_edit = QLineEdit()
        form_layout.addRow("查找内容:", self.modify_find_edit)
        form_layout.addRow("替换内容:", self.modify_replace_edit)

        find_replace_section.addWidget(form_widget)
        main_layout.addLayout(find_replace_section)

        # ==================== 高级选项区块 ====================
        option_section = QVBoxLayout()
        option_section.setContentsMargins(0, 0, 0, 0)
        option_section.setSpacing(8)

        # 标题标签
        option_title = QLabel("高级选项", objectName="basicTitle")
        option_title.setFixedHeight(20)
        option_section.addWidget(option_title)

        # 复选框布局
        check_layout = QVBoxLayout()
        check_layout.setContentsMargins(0, 0, 0, 0)
        check_layout.setSpacing(6)

        self.modify_case_check = QCheckBox("区分大小写")
        self.modify_regex_check = QCheckBox("使用正则表达式")
        check_layout.addWidget(self.modify_case_check)
        check_layout.addWidget(self.modify_regex_check)

        option_section.addLayout(check_layout)
        main_layout.addLayout(option_section)

        # ==================== 自动解决选项 ====================
        # ==================== 格式转换设置 ====================
        convert_section = QVBoxLayout()
        convert_section.setContentsMargins(0, 0, 0, 0)
        convert_section.setSpacing(8)

        convert_title = QLabel("格式转换设置", objectName="basicTitle")
        convert_title.setFixedHeight(20)
        convert_section.addWidget(convert_title)

        style_container = QWidget()
        style_layout = QFormLayout(style_container)
        style_layout.setContentsMargins(0, 0, 0, 0)
        style_layout.setHorizontalSpacing(15)
        style_layout.setVerticalSpacing(10)

        self.case_convert = QComboBox()
        self.case_convert.addItems(["不转换", "首字母大写", "首字母小写", "全大写", "全小写", "单词首字母大写"])
        self.traditional_convert = QComboBox()
        self.traditional_convert.addItems(["不转换", "简转繁", "繁转简"])

        self.hanzi_to_pinyin = QComboBox()
        self.hanzi_to_pinyin.addItems([
            "不转换",    # 保持原字符集
            "中文转拼音",    # 简体转繁体
        ])

        style_layout.addRow(QLabel("英文大小写:"), self.case_convert)
        style_layout.addRow(QLabel("中文繁简体:"), self.traditional_convert)
        style_layout.addRow(QLabel("中文转拼音:"), self.hanzi_to_pinyin)

        # 修改后的分割符对调设置（水平布局）
        swap_container = QWidget()
        swap_layout = QVBoxLayout(swap_container)  # 改为水平布局
        swap_layout.setContentsMargins(0, 0, 0, 0)
        swap_layout.setSpacing(4)

        # 使用水平布局组合标签和输入框
        swap_layout.addWidget(QLabel("根据分割符对调内容："))       # 修正错别字并简化文本
        self.delimiter_swap_edit = QLineEdit()
        self.delimiter_swap_edit.setPlaceholderText("AA-BB转BB-AA，输入-")
        swap_layout.addWidget(self.delimiter_swap_edit)


        convert_section.addWidget(style_container)
        convert_section.addWidget(swap_container)
        main_layout.addLayout(convert_section)

        self.auto_resolve_check_modify = QCheckBox("自动解决命名重复冲突")
        self.auto_resolve_check_modify.setChecked(True)
        main_layout.addWidget(self.auto_resolve_check_modify)

        # 添加弹性空间使内容顶部对齐
        main_layout.addStretch()

        self.tab_widget.addTab(tab, "修改")

    def setup_delete_tab(self):
        """删除功能标签页（重构后）"""
        tab = QWidget()

        # 主布局使用垂直布局，设置统一边距和间距
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(12, 12, 12, 12)  # 外边界
        main_layout.setSpacing(15)  # 部件间距

        # ==================== 删除模式选择 ====================
        mode_section = QVBoxLayout()
        mode_section.setContentsMargins(0, 0, 0, 0)
        mode_section.setSpacing(8)

        # 模式标题
        mode_title = QLabel("删除方式", objectName="basicTitle")
        mode_title.setFixedHeight(20)
        mode_section.addWidget(mode_title)

        # 单选按钮组
        self.delete_mode_group = QButtonGroup()
        self.rb_content = QRadioButton("按内容", checked=True)
        self.rb_position = QRadioButton("按位置")
        self.rb_type = QRadioButton("按类型")

        # 单选按钮布局（增加6px间距）
        radio_layout = QHBoxLayout()
        radio_layout.setContentsMargins(0, 0, 0, 0)
        radio_layout.setSpacing(6)
        radio_layout.addWidget(self.rb_content)
        radio_layout.addWidget(self.rb_position)
        radio_layout.addWidget(self.rb_type)

        # 添加按钮组关系
        self.delete_mode_group.addButton(self.rb_content)
        self.delete_mode_group.addButton(self.rb_position)
        self.delete_mode_group.addButton(self.rb_type)

        mode_section.addLayout(radio_layout)
        main_layout.addLayout(mode_section)

        # ==================== 按内容删除设置 ====================
        self.content_widget = QWidget()
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(8)

        # 内容标题
        content_title = QLabel("按内容删除", objectName="basicTitle")
        content_title.setFixedHeight(20)
        content_layout.addWidget(content_title)

        # 表单布局（水平间距15px）
        form_layout = QFormLayout()
        form_layout.setHorizontalSpacing(15)
        self.delete_content_edit = QLineEdit()
        form_layout.addRow("删除内容:", self.delete_content_edit)
        content_layout.addLayout(form_layout)

        main_layout.addWidget(self.content_widget)

        # ==================== 按位置删除设置 ====================
        self.position_widget = QWidget()
        position_layout = QVBoxLayout(self.position_widget)
        position_layout.setContentsMargins(0, 0, 0, 0)
        position_layout.setSpacing(8)

        # 位置标题
        position_title = QLabel("按位置删除", objectName="basicTitle")
        position_title.setFixedHeight(20)
        position_layout.addWidget(position_title)

        # 表单布局
        pos_form = QFormLayout()
        pos_form.setHorizontalSpacing(15)
        self.position_start = QSpinBox()
        self.position_length = QSpinBox()
        pos_form.addRow("起始位置:", self.position_start)
        pos_form.addRow("删除长度:", self.position_length)
        position_layout.addLayout(pos_form)

        main_layout.addWidget(self.position_widget)

        # ==================== 按类型删除设置 ====================
        self.type_widget = QWidget()
        type_layout = QVBoxLayout(self.type_widget)
        type_layout.setContentsMargins(0, 0, 0, 0)
        type_layout.setSpacing(8)

        # 类型标题
        type_title = QLabel("按类型删除", objectName="basicTitle")
        type_title.setFixedHeight(20)
        type_layout.addWidget(type_title)

        # 复选框布局（6px间距）
        check_layout = QVBoxLayout()
        check_layout.setContentsMargins(0, 0, 0, 0)
        check_layout.setSpacing(6)
        self.cb_english = QCheckBox("英文字符")
        self.cb_chinese = QCheckBox("中文字符")
        self.cb_digit = QCheckBox("数字")
        self.cb_special = QCheckBox("特殊字符")
        check_layout.addWidget(self.cb_english)
        check_layout.addWidget(self.cb_chinese)
        check_layout.addWidget(self.cb_digit)
        check_layout.addWidget(self.cb_special)

        type_layout.addLayout(check_layout)
        main_layout.addWidget(self.type_widget)

        # ==================== 自动解决选项 ====================
        self.auto_resolve_check_delete = QCheckBox("自动解决命名重复冲突")
        self.auto_resolve_check_delete.setChecked(True)
        main_layout.addWidget(self.auto_resolve_check_delete)

        # 初始可见性控制
        self.content_widget.setVisible(True)
        self.position_widget.setVisible(False)
        self.type_widget.setVisible(False)

        # 单选按钮切换事件
        self.rb_content.toggled.connect(lambda: self._update_delete_ui_visibility("rb_content"))
        self.rb_position.toggled.connect(lambda: self._update_delete_ui_visibility("rb_position"))
        self.rb_type.toggled.connect(lambda: self._update_delete_ui_visibility("rb_type"))

        # 添加弹性空间使内容顶部对齐
        main_layout.addStretch()
        self.tab_widget.addTab(tab, "删除")

    def _update_delete_ui_visibility(self, mode):
        """更新删除UI可见性"""
        self.content_widget.setVisible(mode == "rb_content")
        self.position_widget.setVisible(mode == "rb_position")
        self.type_widget.setVisible(mode == "rb_type")

    def _handle_delete_rule(self, filename):
        """处理删除规则（重构后）"""
        processed_name = filename

        # 根据选择的删除模式处理
        if self.rb_content.isChecked():
            # 按内容删除
            target = self.delete_content_edit.text()
            processed_name = StringUtils.delete_string_content(processed_name, target)
        elif self.rb_position.isChecked():
            # 按位置删除
            start = self.position_start.value() - 1  # 转换为0-based
            length = self.position_length.value()

            # 输入有效性验证
            if start < 0 or length <= 0:
                return filename
            if start >= len(processed_name):
                return filename

            end = min(start + length, len(processed_name))
            processed_name = processed_name[:start] + processed_name[end:]

        elif self.rb_type.isChecked():
            # 按类型删除（使用正则表达式）
            patterns = []
            if self.cb_english.isChecked():
                patterns.append(r'[A-Za-z]')
            if self.cb_chinese.isChecked():
                patterns.append(r'[\u4e00-\u9fff]')
            if self.cb_digit.isChecked():
                patterns.append(r'\d')
            if self.cb_special.isChecked():
                patterns.append(r'[^\w\s]')

            if patterns:
                regex_pattern = '|'.join(patterns)
                processed_name = re.sub(regex_pattern, '', processed_name)

        return processed_name

    def setup_custom_tab(self):
        """自定义功能标签页"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # 脚本编辑器
        self.custom_script_edit = QTextEdit()

        self.custom_script_edit.setPlaceholderText( "实例1：文件夹名              \n"
                                                    "实例2：旧文件夹名<空格>新文件夹名 \n"
                                                    "实例3：查找内容<空格>替换为\n"
                                                    "\n"
                                                    "\n"
                                                    "每行对应一个文件夹命名，与左侧列表一一对应"
                                                    )

        layout.addRow(self.custom_script_edit)#"自定义脚本:",

        # 示例说明
        example_group = QGroupBox("示例")
        example_layout = QVBoxLayout(example_group)
        example_layout.addWidget(QLabel("文件夹名                → 替换"))
        example_layout.addWidget(QLabel("旧文件夹<空格>新文件夹 → 查找对应"))
        example_layout.addWidget(QLabel("查找内容<空格>替换为   → 名字部分替换"))

        layout.addRow(example_group)

        self.auto_resolve_check_custom = QCheckBox("自动解决命名重复冲突")
        self.auto_resolve_check_custom.setChecked(True)
        layout.addRow(self.auto_resolve_check_custom)

        self.tab_widget.addTab(tab, "自定义")

    def setup_sequence_settings(self, layout, prefix):
        """通用序列生成设置（用于新命名和增加标签页）"""
        # 为兼容性创建position_spin，但不在界面上显示
        self.__dict__[f"{prefix}position_spin"] = QSpinBox()
        self.__dict__[f"{prefix}position_spin"].setVisible(False)
        # 数据类型
        type_group = QWidget()
        type_layout = QHBoxLayout(type_group)
        type_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)  # 添加对齐设置
        type_layout.setSpacing(20)  # 设置单选按钮之间的间距

        title_container = QVBoxLayout() # 水平布局容器
        title_container.setContentsMargins(0, 0, 0, 0)  # 与图片左侧缩进12px一致
        title_container.setSpacing(12)
        self.title_label = QLabel("数据类型", objectName="basicTitle")
        self.title_label.setFixedHeight(20)  # 高度缩减为标题文字的0.8倍 (从24px缩短)
        title_container.addWidget(self.title_label)
        layout.addRow(title_container)

        self.__dict__[f"{prefix}num_radio"] = QRadioButton("数字", checked=True)
        self.__dict__[f"{prefix}letter_radio"] = QRadioButton("字母")
        self.__dict__[f"{prefix}date_radio"] = QRadioButton("日期")
        self.__dict__[f"{prefix}random_radio"] = QRadioButton("随机")
        type_layout.addWidget(self.__dict__[f"{prefix}num_radio"])
        type_layout.addWidget(self.__dict__[f"{prefix}letter_radio"])
        type_layout.addWidget(self.__dict__[f"{prefix}date_radio"])
        type_layout.addWidget(self.__dict__[f"{prefix}random_radio"])
        layout.addRow(type_group)
        # 数字设置
        num_container = QWidget()
        self.__dict__[f"{prefix}num_container"] = num_container  # 新增此行
        num_layout = QFormLayout(num_container)
        self.__dict__[f"{prefix}start_num"] = QSpinBox()
        self.__dict__[f"{prefix}digit_length"] = QSpinBox()
        self.__dict__[f"{prefix}increment"] = QSpinBox()
        num_layout.addRow("起始值:", self.__dict__[f"{prefix}start_num"])
        num_layout.addRow("位数:", self.__dict__[f"{prefix}digit_length"])
        num_layout.addRow("增量:", self.__dict__[f"{prefix}increment"])

        self.__dict__[f"{prefix}start_num"].setRange(0,10000000)#起始值
        self.__dict__[f"{prefix}digit_length"].setRange(0,50)#位数
        self.__dict__[f"{prefix}increment"].setRange(-99,99)#增量

        self.__dict__[f"{prefix}start_num"].setValue(1)#起始值  可设置初始值
        self.__dict__[f"{prefix}digit_length"].setValue(1)#位数  可设置初始值
        self.__dict__[f"{prefix}increment"].setValue(1)#增量     可设置初始值

        layout.addRow(num_container)
        num_container.setVisible(True)
        # 字母设置
        letter_container = QWidget()
        self.__dict__[f"{prefix}letter_container"] = letter_container  # 新增此行
        letter_layout = QFormLayout(letter_container)
        self.__dict__[f"{prefix}letter_start"] = QSpinBox()

        self.__dict__[f"{prefix}letter_start"].setRange(1, 16384)  # Excel最大列号XFD=16384
        self.__dict__[f"{prefix}letter_start"].setToolTip("Excel列号规则: 1=A, 26=Z, 27=AA, 28=AB...")

        self.__dict__[f"{prefix}letter_start"].setValue(1) #可设置初始值
        self.__dict__[f"{prefix}letter_step"] = QSpinBox()
        letter_layout.addRow("起始字母:", self.__dict__[f"{prefix}letter_start"])
        letter_layout.addRow("步长:", self.__dict__[f"{prefix}letter_step"])

        self.__dict__[f"{prefix}letter_step"].setValue(1)#可设置初始值

        layout.addRow(letter_container)
        letter_container.setVisible(False)

        # 日期设置
        date_container = QWidget()
        self.__dict__[f"{prefix}date_container"] = date_container  # 新增此行
        date_layout = QFormLayout(date_container)
        self.__dict__[f"{prefix}date_type"] = QComboBox()
        self.__dict__[f"{prefix}date_format"] = QComboBox()
        self.__dict__[f"{prefix}date_type"].addItems(["创建时间", "修改时间", "当前时间"])
        self.__dict__[f"{prefix}date_format"].addItems(["2000年01月01日", "20000101" ,"2000-01-01" , "2000-01-01-16-00-00", "2000年1月1日16时00分00秒","20000101160000"])
        date_layout.addRow("时间类型:", self.__dict__[f"{prefix}date_type"])
        date_layout.addRow("时间格式:", self.__dict__[f"{prefix}date_format"])
        layout.addRow(date_container)
        date_container.setVisible(False)

        # 随机设置
        random_container = QWidget()
        self.__dict__[f"{prefix}random_container"] = random_container  # 新增此行
        random_layout = QFormLayout(random_container)
        self.__dict__[f"{prefix}random_length"] = QSpinBox()
        self.__dict__[f"{prefix}random_type"] = QComboBox()
        self.__dict__[f"{prefix}random_type"].addItems(["数字", "字母", "混合"])
        random_layout.addRow("长度:", self.__dict__[f"{prefix}random_length"])
        random_layout.addRow("类型:", self.__dict__[f"{prefix}random_type"])

        layout.addRow(random_container)
        random_container.setVisible(False)

        self.__dict__[f"{prefix}random_length"].setValue(1)  #起始值 可设置初始值



    def connect_signals(self):
        """连接信号与槽函数"""
        # 公共信号
        self.btn_clear.clicked.connect(self.clear_table)

        self.btn_add_folder.clicked.connect(self.handle_add_folder)
        self.btn_start.clicked.connect(self.execute_rename)
        self.btn_undo.clicked.connect(self.undo_rename)
        self.tab_widget.currentChanged.connect(self.update_current_function)

        # 表头点击事件已在表格初始化时连接到_handle_header_sort

        # 新命名和增加标签页信号
        for prefix in ["basic_", "add_"]:
            self.__dict__[f"{prefix}rule_edit"].textChanged.connect(self.generate_previews)
            self.__dict__[f"{prefix}position_spin"].valueChanged.connect(self.generate_previews)

        # 新命名模块的前缀/后缀信号连接
        if hasattr(self, 'basic_prefix_radio'):
            self.basic_prefix_radio.toggled.connect(self.generate_previews)
        if hasattr(self, 'basic_suffix_radio'):
            self.basic_suffix_radio.toggled.connect(self.generate_previews)

        # 新命名模块的自定义选项信号连接
        if hasattr(self, 'basic_custom_radio'):
            self.basic_custom_radio.toggled.connect(self.generate_previews)
            if hasattr(self, 'basic_custom_position'):
                self.basic_custom_position.valueChanged.connect(self.generate_previews)



        # 增加模块的插入位置选项信号连接
        if hasattr(self, 'add_prefix_radio'):
            self.add_prefix_radio.toggled.connect(self.generate_previews)
        if hasattr(self, 'add_suffix_radio'):
            self.add_suffix_radio.toggled.connect(self.generate_previews)
        if hasattr(self, 'add_custom_radio'):
            self.add_custom_radio.toggled.connect(self.generate_previews)
            if hasattr(self, 'add_custom_position'):
                self.add_custom_position.valueChanged.connect(self.generate_previews)

        # 增加模块的序列号位置选项信号连接
        if hasattr(self, 'add_seq_prefix_radio'):
            self.add_seq_prefix_radio.toggled.connect(self.generate_previews)
        if hasattr(self, 'add_seq_suffix_radio'):
            self.add_seq_suffix_radio.toggled.connect(self.generate_previews)
        if hasattr(self, 'add_seq_custom_radio'):
            self.add_seq_custom_radio.toggled.connect(self.generate_previews)
            if hasattr(self, 'add_seq_custom_position'):
                self.add_seq_custom_position.valueChanged.connect(self.generate_previews)

        # 增加模块的编号设置复选框信号连接
        if hasattr(self, 'add_sequence_enabled'):
            self.add_sequence_enabled.toggled.connect(self.generate_previews)

        # 增加模块的插入名称内容变化信号连接（用于更新序列号位置范围）
        if hasattr(self, 'add_rule_edit'):
            self.add_rule_edit.textChanged.connect(self._update_seq_custom_position_range)

        # 增加模块的插入位置模式变化信号连接（用于更新序列号位置范围）
        if hasattr(self, 'add_prefix_radio'):
            self.add_prefix_radio.toggled.connect(self._update_seq_custom_position_range)
        if hasattr(self, 'add_suffix_radio'):
            self.add_suffix_radio.toggled.connect(self._update_seq_custom_position_range)
        if hasattr(self, 'add_custom_radio'):
            self.add_custom_radio.toggled.connect(self._update_seq_custom_position_range)

        # 继续处理数据类型信号连接
        for prefix in ["basic_", "add_"]:
            for data_type in ["num", "letter", "date", "random"]:
                self.__dict__[f"{prefix}{data_type}_radio"].toggled.connect(self.generate_previews)
            # 新命名标签页信号

            self.__dict__[f"{prefix}start_num"].valueChanged.connect(self.generate_previews)#起始值  可设置初始值
            self.__dict__[f"{prefix}digit_length"].valueChanged.connect(self.generate_previews)#位数  可设置初始值
            self.__dict__[f"{prefix}increment"].valueChanged.connect(self.generate_previews)#增量     可设置初始值

            self.__dict__[f"{prefix}letter_start"].valueChanged.connect(self.generate_previews)
            self.__dict__[f"{prefix}letter_step"].valueChanged.connect(self.generate_previews)#增量     可设置初始值

            self.__dict__[f"{prefix}date_type"].currentIndexChanged.connect(self.generate_previews)
            self.__dict__[f"{prefix}date_format"].currentIndexChanged.connect(self.generate_previews)

            self.__dict__[f"{prefix}random_length"].valueChanged.connect(self.generate_previews)#增量     可设置初始值
            self.__dict__[f"{prefix}random_type"].currentIndexChanged.connect(self.generate_previews)
            #QComboBox
        # 修改标签页信号
        self.modify_find_edit.textChanged.connect(self.generate_previews)
        self.modify_replace_edit.textChanged.connect(self.generate_previews)
        self.modify_case_check.stateChanged.connect(self.generate_previews)
        self.modify_regex_check.stateChanged.connect(self.generate_previews)
        self.delimiter_swap_edit.textChanged.connect(self.generate_previews)
        # 新增格式转换信号
        self.case_convert.currentIndexChanged.connect(self.generate_previews)
        self.traditional_convert.currentIndexChanged.connect(self.generate_previews)
        self.hanzi_to_pinyin.currentIndexChanged.connect(self.generate_previews)


        # 删除标签页信号
        self.rb_content.toggled.connect(self.generate_previews)
        self.rb_position.toggled.connect(self.generate_previews)
        self.rb_type.toggled.connect(self.generate_previews)
        self.delete_content_edit.textChanged.connect(self.generate_previews)
        self.position_start.valueChanged.connect(self.generate_previews)
        self.position_length.valueChanged.connect(self.generate_previews)
        self.cb_english.stateChanged.connect(self.generate_previews)
        self.cb_chinese.stateChanged.connect(self.generate_previews)
        self.cb_digit.stateChanged.connect(self.generate_previews)
        self.cb_special.stateChanged.connect(self.generate_previews)

        # 自定义标签页信号
        self.custom_script_edit.textChanged.connect(self.generate_previews)

    def setup_conditional_visibility(self):
        """设置条件可见性"""
        for prefix in ["basic_", "add_"]:
            self.__dict__[f"{prefix}num_radio"].toggled.connect(
                lambda checked, p=prefix: self.__dict__[f"{p}num_container"].setVisible(checked))
            self.__dict__[f"{prefix}letter_radio"].toggled.connect(
                lambda checked, p=prefix: self.__dict__[f"{p}letter_container"].setVisible(checked))
            self.__dict__[f"{prefix}date_radio"].toggled.connect(
                lambda checked, p=prefix: self.__dict__[f"{p}date_container"].setVisible(checked))
            self.__dict__[f"{prefix}random_radio"].toggled.connect(
                lambda checked, p=prefix: self.__dict__[f"{p}random_container"].setVisible(checked))

    def update_current_function(self, index):
        """更新当前激活的功能标签"""
        self.current_function = index
        self.generate_previews()


    def generate_previews(self):
        """生成所有文件的新名称预览，增强错误处理"""
        self.table.setUpdatesEnabled(False)
        error_rows = []
        
        try:
            for row in range(self.table.rowCount()):
                original_item = self.table.item(row, 0)
                if original_item is None:
                    continue

                original = original_item.text()
                try:
                    new_name = self.generate_new_name(original, row)
                    
                    new_name_item = self.table.item(row, 2)
                    if new_name_item is None:
                        new_name_item = QTableWidgetItem()
                        self.table.setItem(row, 2, new_name_item)

                    new_name_item.setText(new_name)
                    
                    # 更新状态列
                    status_item = self.table.item(row, 3)
                    if status_item and status_item.text().startswith("❌ 预览错误"):
                        status_item.setText("等待处理")
                        
                except Exception as e:
                    error_rows.append(row)
                    # 清空新名称
                    new_name_item = self.table.item(row, 2)
                    if new_name_item:
                        new_name_item.setText("")
                    
                    # 更新状态列显示错误
                    status_item = self.table.item(row, 3)
                    if status_item:
                        status_item.setText(f"❌ 预览错误: {str(e)[:30]}...")
            
            # 如果有错误，更新状态标签
            if error_rows:
                self.status_label.setText(f"预览生成出现 {len(error_rows)} 个错误")
                self.status_label.setStyleSheet("color: orange;")
                QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
                
        except Exception as e:
            self.status_label.setText(f"预览生成失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
        finally:
            self.table.setUpdatesEnabled(True)


    def generate_new_name(self, filename: str, index: int) -> str:
        """根据当前激活的标签页生成新文件名"""
        # 文件夹无需处理扩展名
        try:
            if self.current_function == 0:  # 新命名
                return self._handle_basic_rename(filename, index)
            elif self.current_function == 1:  # 增加
                return self._handle_add_rule(filename, index)
            elif self.current_function == 2:  # 修改
                return self._handle_modify_rule(filename)  # 修改后的方法已适配文件夹
            elif self.current_function == 3:  # 删除
                return self._handle_delete_rule(filename)
            elif self.current_function == 4:  # 自定义
                return self._handle_custom_rule(filename, index)
        except Exception as e:
            return f"错误: {str(e)}"

    def _handle_basic_rename(self, filename, index):
        """处理新命名规则"""
        return self._generate_sequence_name(filename, index, prefix="basic_")

    def _handle_add_rule(self, filename, index):

        return self._generate_sequence_name(filename, index, prefix="add_")

    def _get_max_combined_length(self, base_rule):
        """
        计算所有文件夹组合后名称的最大长度
        用于设置序列号自定义位置的最大值

        Args:
            base_rule: 插入的名称内容

        Returns:
            int: 最大组合长度
        """
        if not hasattr(self, 'table') or self.table.rowCount() == 0:
            # 如果没有表格或表格为空，返回默认值
            return len(base_rule) + 50  # 假设文件夹名最长50字符

        max_length = 0

        # 遍历所有文件夹，计算组合后的最大长度
        for row in range(self.table.rowCount()):
            try:
                folder_name = self.table.item(row, 0).text()

                # 根据当前选择的插入位置模式计算组合长度
                if hasattr(self, 'add_prefix_radio') and self.add_prefix_radio.isChecked():
                    # 前缀模式：插入名称 + 原文件夹名
                    combined_length = len(base_rule) + len(folder_name)
                elif hasattr(self, 'add_suffix_radio') and self.add_suffix_radio.isChecked():
                    # 后缀模式：原文件夹名 + 插入名称
                    combined_length = len(folder_name) + len(base_rule)
                elif hasattr(self, 'add_custom_radio') and self.add_custom_radio.isChecked():
                    # 自定义模式：原文件夹名 + 插入名称（插入在中间）
                    combined_length = len(folder_name) + len(base_rule)
                else:
                    # 默认前缀模式
                    combined_length = len(base_rule) + len(folder_name)

                max_length = max(max_length, combined_length)

            except (AttributeError, TypeError):
                # 如果某行数据有问题，跳过
                continue

        # 如果计算结果为0，返回默认值
        return max_length if max_length > 0 else len(base_rule) + 50

    def _get_max_folder_name_length(self):
        """
        计算所有文件夹名的最大长度
        用于设置插入位置自定义位置的最大值

        Returns:
            int: 最大文件夹名长度
        """
        if not hasattr(self, 'table') or self.table.rowCount() == 0:
            # 如果没有表格或表格为空，返回默认值
            return 50  # 假设文件夹名最长50字符

        max_length = 0

        # 遍历所有文件夹，找到最长的文件夹名
        for row in range(self.table.rowCount()):
            try:
                folder_name = self.table.item(row, 0).text()
                max_length = max(max_length, len(folder_name))
            except (AttributeError, TypeError):
                # 如果某行数据有问题，跳过
                continue

        # 如果计算结果为0，返回默认值
        return max_length if max_length > 0 else 50

    def _update_seq_custom_position_range(self):
        """
        更新序列号自定义位置的范围
        当插入名称内容或插入位置模式改变时调用
        """
        if hasattr(self, 'add_seq_custom_position') and hasattr(self, 'add_rule_edit'):
            base_rule = self.add_rule_edit.toPlainText()
            max_combined_length = self._get_max_combined_length(base_rule)
            self.add_seq_custom_position.setRange(0, max_combined_length)

    def _update_insert_custom_position_range(self):
        """
        更新插入位置自定义位置的范围
        当文件列表变化时调用
        """
        if hasattr(self, 'add_custom_position'):
            max_folder_name_length = self._get_max_folder_name_length()
            self.add_custom_position.setRange(0, max_folder_name_length)


    def _generate_sequence_name(self, filename, index, prefix):
        """生成序列化名称（通用方法）"""
        name = filename  # 文件夹没有扩展名
        folder_path = self.table.item(index, 1).text()
        base_rule = self.__dict__[f"{prefix}rule_edit"].toPlainText()
        self.__dict__[f"{prefix}position_spin"].setRange(0, len(name))  # 设置插入位置为原名称长度

        # 设置自定义位置控件的范围
        if prefix == "basic_" and hasattr(self, 'basic_custom_position'):
            self.basic_custom_position.setRange(0, len(base_rule))
        elif prefix == "add_" and hasattr(self, 'add_custom_position'):
            # 计算所有文件夹名的最大长度
            max_folder_name_length = self._get_max_folder_name_length()
            self.add_custom_position.setRange(0, max_folder_name_length)

        # 设置序列号自定义位置控件的范围（仅适用于add_前缀）
        if prefix == "add_" and hasattr(self, 'add_seq_custom_position'):
            # 计算所有文件夹组合后名称的最大长度
            max_combined_length = self._get_max_combined_length(base_rule)
            self.add_seq_custom_position.setRange(0, max_combined_length)
        # 生成序列内容
        if self.__dict__[f"{prefix}num_radio"].isChecked():
            start = self.__dict__[f"{prefix}start_num"].value()
            digits = self.__dict__[f"{prefix}digit_length"].value()
            step = self.__dict__[f"{prefix}increment"].value()
            sequence = f"{(start + index * step):0{digits}d}"
        elif self.__dict__[f"{prefix}letter_radio"].isChecked():
            # Excel列号转换逻辑
            def number_to_excel_col(num):
                if num <= 0:
                    return ""
                letters = []
                while num > 0:
                    num, remainder = divmod(num - 1, 26)
                    letters.append(chr(65+remainder))
                return ''.join(reversed(letters))
            base_number = self.__dict__[f"{prefix}letter_start"].value()
            step = self.__dict__[f"{prefix}letter_step"].value()
            current_number = base_number + index * step
            if current_number < 1:
                return "无效数值"
            sequence = number_to_excel_col(current_number)

        elif self.__dict__[f"{prefix}date_radio"].isChecked():
            date_type = self.__dict__[f"{prefix}date_type"].currentText()
            date_format = self.__dict__[f"{prefix}date_format"].currentText()
            # 根据文件夹创建时间获取时间戳
            if date_type == "创建时间":
                timestamp = os.path.getctime(folder_path)
            elif date_type == "修改时间":
                timestamp = os.path.getmtime(folder_path)
            else:  # 当前时间
                timestamp = time.time()
            # 转换时间戳为QDateTime
            qdatetime = QDateTime.fromSecsSinceEpoch(int(timestamp))
            # 处理中文格式的转换
            format_mapping = {
                "2000年01月01日": "yyyy年MM月dd日",
                "20000101": "yyyyMMdd",
                "2000-01-01": "yyyy-MM-dd",
                "2000-01-01-16-00-00":"yyyy-MM-dd-HH-mm-ss",
                "2000年1月1日16时00分00秒":"yyyy年M月d日H时mm分ss秒",
                "20000101160000":"yyyyMMddHHMMss"
            }
            sequence = qdatetime.toString(format_mapping.get(date_format, date_format))
        elif self.__dict__[f"{prefix}random_radio"].isChecked():
            length = self.__dict__[f"{prefix}random_length"].value()
            random_type = self.__dict__[f"{prefix}random_type"].currentText()
            if random_type == "数字":
                chars = string.digits
            elif random_type == "字母":
                chars = string.ascii_letters
            else:  # 当前时间
                chars = string.digits + string.ascii_letters
            sequence = ''.join(random.choices(chars, k=length))

        if prefix == "basic_":
            if self.__dict__[f"{prefix}prefix_radio"].isChecked():
                # 前缀模式：序列号在前
                return f"{sequence}{base_rule}"
            elif self.__dict__[f"{prefix}suffix_radio"].isChecked():
                # 后缀模式：序列号在后
                return f"{base_rule}{sequence}"
            elif hasattr(self, 'basic_custom_radio') and self.basic_custom_radio.isChecked():
                # 自定义模式：使用自定义位置插入序列号
                custom_pos = self.basic_custom_position.value()
                # 确保插入位置不超过文件名长度
                custom_pos = min(custom_pos, len(base_rule))
                return f"{base_rule[:custom_pos]}{sequence}{base_rule[custom_pos:]}"
            else:
                # 默认后缀模式（兼容性处理）
                return f"{base_rule}{sequence}"

        elif prefix == "add_":
            # 检查是否启用了编号设置
            sequence_enabled = hasattr(self, 'add_sequence_enabled') and self.add_sequence_enabled.isChecked()

            # 检查是否存在自定义插入位置选项并且该选项被选中
            if hasattr(self, 'add_custom_radio') and self.add_custom_radio.isChecked():
                # 获取用户在SpinBox中设置的插入位置值
                custom_pos = self.add_custom_position.value()

                # 确保插入位置不超过原文件名长度
                custom_pos = min(custom_pos, len(name))

                # 第一阶段：原文件名与插入名称的组合
                combined_name = f"{name[:custom_pos]}{base_rule}{name[custom_pos:]}"

                # 如果未启用编号设置，直接返回组合名称
                if not sequence_enabled:
                    return combined_name

                # 第二阶段：序列号与第一阶段结果的组合
                if hasattr(self, 'add_seq_prefix_radio') and self.add_seq_prefix_radio.isChecked():
                    # 序列号在前：序列号+组合名称
                    return f"{sequence}{combined_name}"
                elif hasattr(self, 'add_seq_suffix_radio') and self.add_seq_suffix_radio.isChecked():
                    # 序列号在后：组合名称+序列号
                    return f"{combined_name}{sequence}"
                elif hasattr(self, 'add_seq_custom_radio') and self.add_seq_custom_radio.isChecked():
                    # 序列号在自定义位置：组合名称前部分+序列号+组合名称后部分
                    seq_pos = self.add_seq_custom_position.value()
                    # 确保序列号位置不超过组合名称长度
                    seq_pos = min(seq_pos, len(combined_name))
                    return f"{combined_name[:seq_pos]}{sequence}{combined_name[seq_pos:]}"
                else:
                    # 默认序列号在后
                    return f"{combined_name}{sequence}"

            elif hasattr(self, 'add_prefix_radio') and self.add_prefix_radio.isChecked():
                # 前缀模式：插入名称+原文件名
                combined_name = f"{base_rule}{name}"

                # 如果未启用编号设置，直接返回组合名称
                if not sequence_enabled:
                    return combined_name

                # 第二阶段：序列号与第一阶段结果的组合
                if hasattr(self, 'add_seq_prefix_radio') and self.add_seq_prefix_radio.isChecked():
                    # 序列号在前：序列号+组合名称
                    return f"{sequence}{combined_name}"
                elif hasattr(self, 'add_seq_suffix_radio') and self.add_seq_suffix_radio.isChecked():
                    # 序列号在后：组合名称+序列号
                    return f"{combined_name}{sequence}"
                elif hasattr(self, 'add_seq_custom_radio') and self.add_seq_custom_radio.isChecked():
                    # 序列号在自定义位置：组合名称前部分+序列号+组合名称后部分
                    seq_pos = self.add_seq_custom_position.value()
                    # 确保序列号位置不超过组合名称长度
                    seq_pos = min(seq_pos, len(combined_name))
                    return f"{combined_name[:seq_pos]}{sequence}{combined_name[seq_pos:]}"
                else:
                    # 默认序列号在前
                    return f"{sequence}{combined_name}"

            else:  # 后缀模式或默认
                # 后缀模式：原文件名+插入名称
                combined_name = f"{name}{base_rule}"

                # 如果未启用编号设置，直接返回组合名称
                if not sequence_enabled:
                    return combined_name

                # 第二阶段：序列号与第一阶段结果的组合
                if hasattr(self, 'add_seq_prefix_radio') and self.add_seq_prefix_radio.isChecked():
                    # 序列号在前：序列号+组合名称
                    return f"{sequence}{combined_name}"
                elif hasattr(self, 'add_seq_suffix_radio') and self.add_seq_suffix_radio.isChecked():
                    # 序列号在后：组合名称+序列号
                    return f"{combined_name}{sequence}"
                elif hasattr(self, 'add_seq_custom_radio') and self.add_seq_custom_radio.isChecked():
                    # 序列号在自定义位置：组合名称前部分+序列号+组合名称后部分
                    seq_pos = self.add_seq_custom_position.value()
                    # 确保序列号位置不超过组合名称长度
                    seq_pos = min(seq_pos, len(combined_name))
                    return f"{combined_name[:seq_pos]}{sequence}{combined_name[seq_pos:]}"
                else:
                    # 默认序列号在后
                    return f"{combined_name}{sequence}"

    def _handle_modify_rule(self, filename):
        """处理查找替换逻辑"""
        find_str = self.modify_find_edit.text()
        replace_str = self.modify_replace_edit.text()
        delimiter_str = self.delimiter_swap_edit.text()

        if self.modify_regex_check.isChecked():
            flags = 0 if self.modify_case_check.isChecked() else re.IGNORECASE
            processed_name = re.sub(find_str, replace_str, filename, flags=flags)
        else:
            if self.modify_case_check.isChecked():
                processed_name = filename.replace(find_str, replace_str)
            else:
                processed_name = filename.lower().replace(find_str.lower(), replace_str)

        # 汉字转拼音
        hanzi_type = self.hanzi_to_pinyin.currentText()
        if hanzi_type != "不转换":
            try:
                processed_name = StringUtils.hanzi_to_pinyin(processed_name, errors="default",separator="")
            except ImportError as e:
                return f"需要安装依赖库: {str(e)}"
            except Exception as e:
                return f"发生错误: {str(e)}"

        # 执行英文格式转换
        case_type = self.case_convert.currentText()
        if case_type != "不转换":
            try:
                processed_name = StringUtils.letter_case_conversion(processed_name, case_type)
            except Exception as e:
                return f"错误: {str(e)}"

        # 执行中文简繁转换
        trad_type = self.traditional_convert.currentText()
        if trad_type != "不转换":
            try:
                processed_name = StringUtils.chinese_conversion(processed_name, trad_type)
            except ImportError as ie:
                return f"错误: {str(ie)}"
            except Exception as e:
                return f"错误: {str(e)}"

        try:
            processed_name=StringUtils.swap_separator_sides(processed_name, delimiter_str)
        except ValueError as e:
            return f"{processed_name}"

        return processed_name


    def _handle_custom_rule(self, filename, index):
        """处理自定义规则"""
        name = filename  # 文件夹没有扩展名
        script = self.custom_script_edit.toPlainText()
        # 支持两种脚本模式：简单列表和映射表
                # 新增文本解析功能
        # 新增输入验证
        if not script.strip():
            return f"{name}"  # 空脚本返回原名
        if ";" in script or "\\n" in script:
            return "错误: 禁止使用多行代码"
        lines = script.strip().split('\n')

        # 模式1：简单列表
        if all(not any(c in line for c in ['\t', ' ']) for line in lines if line.strip()):
            try:

                # 直接按行号匹配
                if index >= len(lines) or not lines[index].strip():
                    return name

                if index < len(lines):
                    return lines[index].strip()
            except Exception as e:
                #return f"列表错误: {str(e)}"
                return f"输入文件夹名"

        # 模式2：映射表
        else:
            mapping = {}
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                # 支持制表符和空格分隔
                parts = re.split(r'[\t ]+', line, 1)
                if len(parts) == 2:
                    old_name, new_name = parts
                    mapping[old_name.strip()] = new_name.strip()

            # 精确匹配
            if filename in mapping:
                return f"{mapping[filename]}"

            # 模糊匹配（包含）
            for old_name, new_name in mapping.items():
                if old_name in filename:
                    return filename.replace(old_name, new_name)

        # 创建安全执行环境
        loc = {
               'filename': name,
                'index': index,
                '__builtins__': {
                    'str': str,
                    'len': len
                }
        }


        try:
            if not script.strip():
                return f"{name}"  # 空脚本返回原名
            exec(f'result = {script}', loc, loc)

            return str(loc.get('result', filename))  # 强制转换为字符串
        except Exception as e:
            #return f"脚本错误: {str(e)}"
            return f"输入文件夹名"

    def _handle_original_script(self, loc, script, ext):
        """处理自定义脚本逻辑（修复语法错误）"""
        try:
            # 移除开头的 "return" 关键字，仅保留表达式
            script = script.strip()
            if script.startswith("return"):
                script = script.replace("return", "", 1).strip()

            # 安全执行环境（允许基础内置函数）
            allowed_builtins = {"__builtins__": {"str": str, "int": int, "len": len}}
            loc.update(allowed_builtins)

            # 执行表达式并捕获结果
            exec(f"result = {script}", loc)
            result = loc.get("result", "")
            result = str(result)

            # 自动补全扩展名（如果用户未添加）
            if not result.endswith(ext):
                result += ext
            return result
        except Exception as e:
            return f"脚本错误: {str(e)}"

    def _get_current_auto_resolve(self):
        """获取当前标签页的自动解决设置"""
        checks = {
            0: self.auto_resolve_check,
            1: self.auto_resolve_check_add,
            2: self.auto_resolve_check_modify,
            3: self.auto_resolve_check_delete,
            4: self.auto_resolve_check_custom
        }
        return checks.get(self.current_function, self.auto_resolve_check).isChecked()

    def _add_to_table(self, file_path):
        """添加单个文件夹到表格，增强错误处理"""
        try:
            # 检查路径是否存在
            if not os.path.exists(file_path):
                self.status_label.setText(f"错误: 文件夹不存在 - {file_path}")
                self.status_label.setStyleSheet("color: red;")
                QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
                return False
                
            # 检查是否为文件夹
            if not os.path.isdir(file_path):
                self.status_label.setText(f"错误: 不是文件夹 - {file_path}")
                self.status_label.setStyleSheet("color: red;")
                QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
                return False
                
            # 检查权限
            try:
                test_file = os.path.join(file_path, ".__test_permission__")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except (PermissionError, IOError):
                self.status_label.setText(f"警告: 可能没有写入权限 - {file_path}")
                self.status_label.setStyleSheet("color: orange;")
                QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
                # 继续添加，但警告用户
            
            path = Path(file_path)
            name = path.name
            full_path = str(path.resolve())

            row = self.table.rowCount()
            self.table.insertRow(row)
            # 文件夹名列（不可编辑）
            item_name = QTableWidgetItem(name)
            item_name.setFlags(item_name.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, item_name)
            item_name.setToolTip(str(name))

            # 原路径列（不可编辑）
            item_path = QTableWidgetItem(full_path)
            item_path.setFlags(item_path.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 1, item_path)
            item_path.setToolTip(str(full_path))

            # 新增排序列和操作列
            self._add_sort_buttons(row)
            self._add_action_buttons(row, str(path))

            # 新文件夹名列（可编辑）
            new_item = QTableWidgetItem("")
            new_item.setFlags(new_item.flags() | Qt.ItemIsEditable)
            self.table.setItem(row, 2, new_item)
            new_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

            # 状态列（不可编辑）
            item_status = QTableWidgetItem("等待处理")
            item_status.setFlags(item_status.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 3, item_status)
            
            return True
            
        except Exception as e:
            import traceback
            self.status_label.setText(f"添加文件夹失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
            print(f"添加文件夹错误: {traceback.format_exc()}")
            return False

    def _add_sort_buttons(self, row):
        """添加排序按钮"""
        sort_widget = QWidget()
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(2)
        btn_up = QPushButton("↑", objectName="sortBtn", maximumWidth=20)
        btn_up.setToolTip("向上移动")  # 添加工具提示
        btn_down = QPushButton("↓", objectName="sortBtn", maximumWidth=20)
        btn_down.setToolTip("向下移动")  # 添加工具提示

        btn_up.clicked.connect(lambda: self._move_row_dynamic(btn_up, -1))
        btn_down.clicked.connect(lambda: self._move_row_dynamic(btn_down, 1))

        btn_layout.addWidget(btn_up)
        btn_layout.addWidget(btn_down)
        btn_layout.setContentsMargins(0,0,0,0)
        sort_widget.setLayout(btn_layout)
        self.table.setCellWidget(row, 4, sort_widget)
        # 设置列宽
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)

    def _move_row_dynamic(self, button, direction):
        """动态获取当前行号"""
        # 检查排序状态并提示用户
        if self._is_table_sorted():
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "确认操作",
                "检测到表格已排序，移动行将清除当前排序。\n是否继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            # 用户选择继续，先重建表格数据以确保数据完整性
            self._rebuild_table_from_file_list()

        cell_widget = button.parent()
        # 更可靠的行号获取方法
        for row in range(self.table.rowCount()):
            if self.table.cellWidget(row, 4) == cell_widget:
                self._move_row(row, direction)
                return
        # 备用方法
        row = self.table.indexAt(cell_widget.pos()).row()
        if row >= 0:
            self._move_row(row, direction)

    def _add_action_buttons(self, row, file_path):
        """添加打开目录和删除按钮"""
        if not os.path.exists(file_path):  # 添加文件存在性检查
            return

        action_widget = QWidget()
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(5)

        btn_open = QPushButton("📁", objectName="btnopen",maximumWidth=30)
        btn_open.clicked.connect(lambda:self.table.setCurrentCell(row, 0))
        btn_open.setToolTip("打开文件夹")  # 添加工具提示
        btn_delete = QPushButton("❌", objectName="btndelete",maximumWidth=30)
        btn_delete.clicked.connect(lambda: self.table.setCurrentCell(row, 0))
        btn_delete.setToolTip("删除")  # 添加工具提示

        btn_open.clicked.connect(lambda: self._open_file_dir(file_path))
        btn_delete.clicked.connect(lambda: self._on_delete_clicked(action_widget))

        btn_layout.addWidget(btn_open)
        btn_layout.addWidget(btn_delete)
        btn_layout.setContentsMargins(0,0,0,0)
        self.table.setCellWidget(row, 5, action_widget)
        action_widget.setLayout(btn_layout)
        self.table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
    def _on_delete_clicked(self, widget):
        """动态获取按钮所在行号并确认删除"""
        row = self.table.indexAt(widget.pos()).row()
        folder_name = self.table.item(row, 0).text()
        
        # 添加删除确认对话框
        confirm = QMessageBox.question(
            self, 
            "确认删除",
            f"确定要从列表中删除文件夹 \"{folder_name}\" 吗？\n此操作不会删除实际的文件夹，仅从列表中移除。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # 默认选中"否"按钮，防止误操作
        )
        
        if confirm == QMessageBox.Yes:
            self._remove_row(row)
            
    def _move_row(self, current_row, direction):
        """移动行位置"""
        if current_row < 0 or current_row >= self.table.rowCount():
            return
        new_row = current_row + direction
        if new_row < 0 or new_row >= self.table.rowCount():
            return

        # 保存所有列的数据，包括widget
        row_data = {}

        # 保存所有列的QTableWidgetItem
        for col in range(self.table.columnCount()):
            item = self.table.item(current_row, col)
            if item is not None:
                # 创建新的item来保存数据
                new_item = QTableWidgetItem(item.text())
                new_item.setData(Qt.UserRole, item.data(Qt.UserRole))
                new_item.setToolTip(item.toolTip())
                row_data[f'item_{col}'] = new_item
            else:
                row_data[f'item_{col}'] = QTableWidgetItem("")

        # 保存所有列的widget
        for col in range(self.table.columnCount()):
            widget = self.table.cellWidget(current_row, col)
            if widget is not None:
                row_data[f'widget_{col}'] = widget
                # 临时移除widget以避免在行移动时丢失
                self.table.setCellWidget(current_row, col, None)

        # 安全地获取文本内容进行验证
        try:
            folder_name = row_data['item_0'].text() if row_data.get('item_0') else ""
            folder_path = row_data['item_1'].text() if row_data.get('item_1') else ""

            # 检查数据完整性
            if not folder_name or not folder_path:
                # 数据不完整，恢复原状态并提示用户
                for col in range(self.table.columnCount()):
                    if f'item_{col}' in row_data:
                        self.table.setItem(current_row, col, row_data[f'item_{col}'])
                    if f'widget_{col}' in row_data:
                        self.table.setCellWidget(current_row, col, row_data[f'widget_{col}'])

                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "提示",
                    "排序状态下数据不完整，请先点击表头清除排序后再移动行")
                return

        except (AttributeError, IndexError) as e:
            # 如果出现任何错误，恢复原状态
            for col in range(self.table.columnCount()):
                if f'item_{col}' in row_data:
                    self.table.setItem(current_row, col, row_data[f'item_{col}'])
                if f'widget_{col}' in row_data:
                    self.table.setCellWidget(current_row, col, row_data[f'widget_{col}'])

            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误",
                f"移动行时发生错误，可能是排序导致的数据不一致。\n请刷新页面后重试。\n错误详情: {str(e)}")
            return

        # 移动行
        self.table.removeRow(current_row)
        self.table.insertRow(new_row)
        self.table.viewport().update()  # 强制刷新布局
        self.table.setCurrentCell(new_row, 0)  # 选中移动状态

        # 恢复所有列的数据
        for col in range(self.table.columnCount()):
            if f'item_{col}' in row_data:
                self.table.setItem(new_row, col, row_data[f'item_{col}'])

        # 恢复所有列的widget（除了排序和操作列，这些需要重新创建）
        for col in range(self.table.columnCount()):
            if col not in [4, 5] and f'widget_{col}' in row_data:  # 4是排序列，5是操作列
                self.table.setCellWidget(new_row, col, row_data[f'widget_{col}'])

        # 重新创建排序和操作按钮
        self._add_sort_buttons(new_row)
        self._add_action_buttons(new_row, folder_path)

        # 移动完成后清除排序状态
        self._clear_sort_indicator()

        # 同时更新文件列表的顺序以保持一致性
        try:
            if 0 <= current_row < len(self.all_files_list) and 0 <= new_row < len(self.all_files_list):
                # 移动文件列表中对应的项
                file_item = self.all_files_list.pop(current_row)
                self.all_files_list.insert(new_row, file_item)
        except (IndexError, ValueError) as e:
            print(f"更新文件列表顺序失败: {e}")
            # 如果文件列表顺序更新失败，重建表格以确保一致性
            self._rebuild_table_from_file_list()

    def _remove_row(self, row):
        """删除指定行并更新状态"""
        if 0 <= row < self.table.rowCount():
            # 保存被删除的路径信息用于状态更新
            path = self.table.item(row, 1).text()
            folder_name = self.table.item(row, 0).text()
            
            # 从内部数据结构中同步删除
            # 使用规范化路径进行比较和删除，确保与dropEvent中的路径检查一致
            norm_path = os.path.normpath(os.path.abspath(path))
            
            # 先找到匹配的路径项然后删除，而不是直接使用原始路径
            for i, stored_path in enumerate(self.all_files_list[:]):
                if os.path.normpath(os.path.abspath(stored_path)) == norm_path:
                    self.all_files_list.pop(i)
                    break
            
            # 从表格中删除行
            self.table.removeRow(row)
            
            # 更新状态标签
            self.status_label.setText(f"已删除文件夹: {folder_name}")
            
            if row >= self.table.rowCount():
                return
                
            # 删除后更新后续行的按钮绑定
            for r in range(row, self.table.rowCount()):
                path = self.table.item(r, 1).text()
                self.table.setCellWidget(r, 5, None)  # 先清除旧的操作按钮
                self._add_action_buttons(r, path)

            # 更新插入位置自定义范围
            self._update_insert_custom_position_range()

    def _is_table_sorted(self):
        """检查表格是否处于排序状态"""
        try:
            header = self.table.horizontalHeader()
            return header.sortIndicatorSection() >= 0
        except:
            return False

    def _clear_sort_indicator(self):
        """清除排序指示器"""
        try:
            header = self.table.horizontalHeader()
            header.setSortIndicator(-1, Qt.AscendingOrder)
        except:
            pass

    def _rebuild_table_from_file_list(self):
        """从文件列表重建表格数据，确保数据完整性"""
        try:
            # 暂时禁用更新以提高性能
            self.table.setUpdatesEnabled(False)

            # 暂时禁用排序
            was_sorting_enabled = self.table.isSortingEnabled()
            self.table.setSortingEnabled(False)

            # 清空表格但保留文件列表
            self.table.setRowCount(0)

            # 重新添加所有文件夹到表格
            for folder_path in self.all_files_list:
                self._add_to_table(folder_path)

            # 清除排序指示器
            self._clear_sort_indicator()

            # 恢复排序状态
            self.table.setSortingEnabled(was_sorting_enabled)

            # 重新生成预览
            self.generate_previews()

        except Exception as e:
            print(f"重建表格数据失败: {e}")
        finally:
            # 恢复更新
            self.table.setUpdatesEnabled(True)

    def _on_header_clicked(self, logical_index):
        """表头点击事件处理"""
        # 延迟重建按钮，确保排序完成
        from PySide6.QtCore import QTimer
        QTimer.singleShot(200, self._rebuild_all_buttons)

    def _rebuild_all_buttons(self):
        """重建所有行的按钮"""
        try:
            for row in range(self.table.rowCount()):
                # 清除旧按钮
                self.table.setCellWidget(row, 4, None)
                self.table.setCellWidget(row, 5, None)

                # 重新创建按钮
                try:
                    path_item = self.table.item(row, 1)
                    folder_item = self.table.item(row, 0)

                    if path_item and folder_item:
                        folder_path = path_item.text()
                        folder_name = folder_item.text()

                        if folder_path and folder_name:
                            self._add_sort_buttons(row)
                            self._add_action_buttons(row, folder_path)
                except Exception as e:
                    print(f"重建第{row}行按钮失败: {e}")
                    # 即使某行失败，也继续处理其他行
                    continue
        except Exception as e:
            print(f"重建所有按钮失败: {e}")

    def _open_file_dir(self, file_path):
        """打开文件所在目录"""
        if not os.path.exists(file_path):
           return
        path = Path(file_path).parent
        QDesktopServices.openUrl(path.as_uri())


    def generate_unique_name(self, original_path: str) -> str:
        """生成唯一文件名"""
        base, ext = os.path.splitext(original_path)
        counter = 1
        while os.path.exists(original_path):
            original_path = f"{base}({counter}){ext}"
            counter += 1
        return original_path

    def undo_rename(self):
        """撤销上一次重命名操作"""
        # 检查界面历史记录
        if not self.rename_history:
            self.status_label.setText("没有可撤销的重命名操作")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
            return
            
        # 获取最后一批操作记录
        last_batch = self.rename_history.pop()
        
        # 使用RenameService进行撤销
        from core.services.rename_service import RenameService
        rename_service = RenameService()
        
        # 将界面记录转换为服务层格式
        for record in last_batch:
            rename_service.last_rename_records.append({
                'old_path': record['old_path'],
                'new_path': record['new_path']
            })
        
        # 更新状态为处理中
        self.status_label.setText("撤销操作处理中...")
        self.btn_undo.setEnabled(False)
        
        # 使用工具方法安全连接信号
        self._safely_connect_signals(rename_service, is_rename=False)
        
        # 执行撤销操作
        rename_service.undo_rename()
        
    def _on_undo_started(self):
        """撤销开始时的处理"""
        self.status_label.setText("开始撤销重命名...")
        
    def _on_undo_progress(self, progress):
        """撤销进度更新"""
        self.status_label.setText(f"撤销进度: {progress}%")
        
    def _on_undo_completed(self, success_records, failed_records):
        """撤销完成时的处理"""
        # 更新表格状态
        success_count = len(success_records)
        failed_count = len(failed_records)
        
        # 更新表格中每个文件夹的状态
        for record in success_records:
            for row in range(self.table.rowCount()):
                if self.table.item(row, 1).text() == record['old_path']:
                    self.table.item(row, 1).setText(record['new_path'])
                    self.table.item(row, 0).setText(os.path.basename(record['new_path']))
                    self.table.item(row, 3).setText("✅ 撤销成功")
                    # 更新操作列的按钮绑定新路径
                    self.table.setCellWidget(row, 5, None)  # 清除旧按钮
                    self._add_action_buttons(row, record['new_path'])  # 重新绑定新路径
                    break
        
        for record in failed_records:
            for row in range(self.table.rowCount()):
                if self.table.item(row, 1).text() == record['old_path']:
                    self.table.item(row, 3).setText(f"❌ 撤销失败: {record['status']}")
                    break
        
        # 更新状态信息
        if failed_count > 0:
            self.status_label.setText(f"撤销完成: {success_count} 个成功, {failed_count} 个失败")
        else:
            self.status_label.setText(f"撤销完成: {success_count} 个操作已恢复")
        
        # 重新生成预览
        self.generate_previews()
        
        # 恢复按钮状态
        self.btn_start.setEnabled(True)

    def _safely_connect_signals(self, rename_service, is_rename=True):
        """安全地连接信号，避免重复连接和警告
        
        Args:
            rename_service: RenameService实例
            is_rename: 是否为重命名操作（True）或撤销操作（False）
        """
        try:
            # 选择正确的处理函数
            if is_rename:
                started_handler = self._on_rename_started
                progress_handler = self._on_rename_progress
                completed_handler = self._on_rename_completed
                error_handler = self._on_rename_error
            else:
                started_handler = self._on_undo_started
                progress_handler = self._on_undo_progress
                completed_handler = self._on_undo_completed
                error_handler = self._on_undo_error
        
            # 直接建立新的连接，不尝试断开旧的连接
            # 由于每次操作都使用新的RenameService实例，这样做应该是安全的
            rename_service.signals.started.connect(started_handler)
            rename_service.signals.progress.connect(progress_handler)
            rename_service.signals.completed.connect(completed_handler)
            rename_service.signals.error.connect(error_handler)
            
        except Exception as e:
            print(f"信号连接错误: {str(e)}")

    def _on_rename_started(self):
        """重命名开始时的处理"""
        self.status_label.setText("开始重命名...")
        
    def _on_rename_progress(self, progress):
        """重命名进度更新"""
        self.status_label.setText(f"重命名进度: {progress}%")
        
    def _on_rename_completed(self, success_records, failed_records):
        """重命名完成时的处理"""
        # 更新表格状态
        success_count = len(success_records)
        failed_count = len(failed_records)
        skip_count = self.table.rowCount() - (success_count + failed_count)
        
        # 创建新旧路径映射，用于更新内部数据结构
        path_mapping = {}
        
        # 更新表格中每个文件夹的状态
        for record in success_records:
            for row in range(self.table.rowCount()):
                if self.table.item(row, 1).text() == record['old_path']:
                    # 记录新旧路径映射
                    path_mapping[record['old_path']] = record['new_path']
                    
                    # 更新表格
                    self.table.item(row, 1).setText(record['new_path'])
                    self.table.item(row, 0).setText(os.path.basename(record['new_path']))
                    self.table.item(row, 3).setText("✅ 成功")
                    
                    # 更新操作列按钮绑定的路径
                    self.table.setCellWidget(row, 5, None)  # 清除旧按钮
                    self._add_action_buttons(row, record['new_path'])  # 重新绑定新路径
                    break
        
        for record in failed_records:
            for row in range(self.table.rowCount()):
                if self.table.item(row, 1).text() == record['old_path']:
                    self.table.item(row, 3).setText(f"❌ 失败: {record['status']}")
                    break
        
        # 更新内部数据结构
        for i, path in enumerate(self.all_files_list):
            if path in path_mapping:
                self.all_files_list[i] = path_mapping[path]
        
        # 更新状态信息
        if skip_count > 0:
            self.status_label.setText(f"完成: {success_count} 个重命名, {failed_count} 个失败, {skip_count} 个无需修改")
        else:
            self.status_label.setText(f"完成: {success_count} 个成功, {failed_count} 个失败")
        
        # 记录到历史以支持撤销
        if success_records:
            batch = []
            for record in success_records:
                if record['status'] != "跳过":
                    batch.append({
                        'old_path': record['old_path'],
                        'new_path': record['new_path']
                    })
            if batch:
                self.rename_history.append(batch)
        
        # 重新生成预览
        self.generate_previews()
        
        # 恢复按钮状态
        self.btn_start.setEnabled(True)

    def _on_rename_error(self, error_message):
        """重命名错误处理"""
        self.status_label.setText(f"重命名失败: {error_message}")
        self.status_label.setStyleSheet("color: red;")
        # 5秒后恢复
        QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
        # 恢复按钮状态
        self.btn_start.setEnabled(True)

    def clear_table(self):
        """清空表格和相关数据"""
        # 暂时禁用排序
        was_sorting_enabled = self.table.isSortingEnabled()
        self.table.setSortingEnabled(False)
        
        # 清空表格
        self.table.setRowCount(0)
        self.all_files_list.clear()  # 清空文件列表
        self.status_label.setText("未加载文件夹")  # 更新状态标签

        # 恢复排序状态
        self.table.setSortingEnabled(was_sorting_enabled)

        # 更新插入位置自定义范围
        self._update_insert_custom_position_range()
        
    def cleanup_resources(self):
        """清理资源，确保在页面关闭时释放所有资源"""
        try:
            # 获取当前使用的RenameService并取消任务
            from core.services.rename_service import RenameService
            rename_service = RenameService()
            rename_service.cancel_current_task()
            
            # 不尝试断开信号，让Python的垃圾回收机制处理
            # 记录一条信息以便于调试
            print("资源清理完成")
            
        except Exception as e:
            print(f"清理资源时出错: {str(e)}")

    def hideEvent(self, event):
        """页面隐藏时清理资源"""
        self.cleanup_resources()
        super().hideEvent(event)
        
    def closeEvent(self, event):
        """页面关闭时清理资源"""
        self.cleanup_resources()
        super().closeEvent(event)

    def handle_add_folder(self):
        """添加文件夹，使用增强的错误处理"""
        # 暂时禁用排序
        was_sorting_enabled = self.table.isSortingEnabled()
        self.table.setSortingEnabled(False)
        
        # 创建已存在文件夹路径的规范化集合
        existing_paths = set()
        for path in self.all_files_list:
            existing_paths.add(os.path.normpath(os.path.abspath(path)))
        
        folder = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder:
            try:
                # 规范化路径以确保一致的比较
                norm_path = os.path.normpath(os.path.abspath(folder))
                
                if norm_path not in existing_paths:
                    self.all_files_list.append(folder)  # 保存原始路径格式
                    existing_paths.add(norm_path)  # 添加到规范化集合中
                    
                    # 使用增强的添加方法
                    if self._add_to_table(folder):
                        self.status_label.setText(f"已添加文件夹: {os.path.basename(folder)}")
                    else:
                        # 添加失败时从列表中删除
                        self.all_files_list.remove(folder)
                        existing_paths.remove(norm_path)
                        self.status_label.setText(f"添加文件夹失败: {os.path.basename(folder)}")
                        self.status_label.setStyleSheet("color: red;")
                        QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
                else:
                    self.status_label.setText(f"文件夹已存在: {os.path.basename(folder)}")
                    self.status_label.setStyleSheet("color: orange;")
                    QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
            except Exception as e:
                self.status_label.setText(f"添加文件夹失败: {str(e)}")
                self.status_label.setStyleSheet("color: red;")
                QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
        
        # 恢复排序状态并刷新预览
        self.table.setSortingEnabled(was_sorting_enabled)
        self.generate_previews()

        # 更新插入位置自定义范围
        self._update_insert_custom_position_range()

    def execute_rename(self):
        """执行重命名操作，使用后端服务处理，优化冲突处理"""
        if self.table.rowCount() == 0:
            self.status_label.setText("没有可操作的文件夹")
            self.status_label.setStyleSheet("color: red;")
            # 5秒后恢复
            QTimer.singleShot(5000, lambda: self.status_label.setStyleSheet(""))
            return

        # 检查许可证限制
        operation_count = self.table.rowCount()
        if not self.license_limiter.check_operation_limit("folder_rename", operation_count, self):
            # 如果用户选择继续处理前N条记录
            limit = self.license_limiter.get_function_limit("folder_rename")
            if limit and operation_count > limit:
                # 显示确认对话框
                reply = QMessageBox.question(
                    self,
                    "确认操作",
                    f"将只处理前 {limit} 条记录，是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
                # 限制操作数量
                operation_count = limit
            else:
                return

        # 使用 RenameService 处理重命名
        from core.services.rename_service import RenameService
        rename_service = RenameService()
        
        # 准备重命名文件夹列表
        rename_folders = []
        empty_new_names = []
        potential_conflicts = set()
        
        # 根据许可证限制确定实际处理的行数
        actual_row_count = min(operation_count, self.table.rowCount())

        for row in range(actual_row_count):
            old_path = self.table.item(row, 1).text()
            new_name_item = self.table.item(row, 2)
            new_name = new_name_item.text() if new_name_item else ""
            
            # 检查新文件夹名是否为空
            if not new_name.strip():
                empty_new_names.append(row + 1)  # 行号从1开始显示给用户
                continue
                
            # 检查文件夹名是否相同
            if os.path.basename(old_path) == new_name:
                self.table.item(row, 3).setText("✅ 相同")
                continue
                
            new_path = os.path.join(os.path.dirname(old_path), new_name)
            
            # 检查是否可能存在命名冲突
            if os.path.exists(new_path) and os.path.normpath(old_path) != os.path.normpath(new_path):
                potential_conflicts.add(new_path)
                
            rename_folders.append({
                'old_path': old_path,
                'new_path': new_path,
                'row': row
            })
        
        # 处理空文件名情况
        if empty_new_names:
            from PySide6.QtWidgets import QMessageBox
            rows_str = ", ".join(map(str, empty_new_names))
            QMessageBox.warning(
                self,
                "重命名错误",
                f"第 {rows_str} 行的新文件夹名为空，请输入有效的文件夹名。"
            )
            return
            
        # 处理潜在命名冲突
        if potential_conflicts and not self.auto_resolve_check.isChecked():
            from PySide6.QtWidgets import QMessageBox
            message = f"检测到 {len(potential_conflicts)} 个可能的命名冲突，是否继续？\n\n选择\"是\"将自动解决冲突，选择\"否\"将取消操作。"
            result = QMessageBox.question(
                self,
                "命名冲突",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if result == QMessageBox.No:
                self.status_label.setText("重命名已取消")
                return
        
        # 如果没有需要重命名的文件夹，直接返回
        if not rename_folders:
            self.status_label.setText("没有需要重命名的文件夹")
            return
        
        # 更新状态为处理中
        self.status_label.setText("文件夹重命名处理中...")
        self.btn_start.setEnabled(False)
        
        # 使用工具方法安全连接信号
        self._safely_connect_signals(rename_service, is_rename=True)
        
        # 执行重命名操作
        rename_service.execute_rename([{
            'old_path': folder['old_path'],
            'new_path': folder['new_path']
        } for folder in rename_folders])

    def dragEnterEvent(self, event):
        """改进拖放提示，统一提醒格式"""
        if event.mimeData().hasUrls():
            # 检查是否全是文件夹
            all_dirs = all(url.isLocalFile() and os.path.isdir(url.toLocalFile())
                           for url in event.mimeData().urls())
            if all_dirs:
                event.acceptProposedAction()
            else:
                event.ignore()
                # 添加状态栏提示，统一格式
                self.status_label.setText("请拖放文件夹（不支持文件）")
                self.status_label.setStyleSheet("color: orange;")
                # 3秒后恢复正常状态
                QTimer.singleShot(3000, lambda: self.status_label.setStyleSheet(""))
        else:
            event.ignore()

    def dropEvent(self, event):
        """改进拖放处理，使用增强的错误处理"""
        # 暂时禁用排序
        was_sorting_enabled = self.table.isSortingEnabled()
        self.table.setSortingEnabled(False)
        
        # 创建已存在文件夹路径的规范化集合
        existing_paths = set()
        for path in self.all_files_list:
            existing_paths.add(os.path.normpath(os.path.abspath(path)))
        
        added_count = 0
        skipped_count = 0
        failed_count = 0
        
        for url in event.mimeData().urls():
            path = url.toLocalFile()
            norm_path = os.path.normpath(os.path.abspath(path))
            
            # 跳过非文件夹
            if not os.path.isdir(path):
                failed_count += 1
                continue
                
            # 跳过已存在的文件夹
            if norm_path in existing_paths:
                skipped_count += 1
                continue
                
            # 使用增强的错误处理添加文件夹
            self.all_files_list.append(path)
            existing_paths.add(norm_path)  # 立即添加到集合中防止同一批次中的重复项
            
            if self._add_to_table(path):
                added_count += 1
            else:
                self.all_files_list.remove(path)  # 添加失败时从列表中删除
                existing_paths.remove(norm_path)  # 同时从规范化路径集合中移除
                failed_count += 1
                
        # 更新状态信息
        if added_count > 0:
            status = f"已添加 {added_count} 个文件夹"
            if skipped_count > 0:
                status += f", 已跳过 {skipped_count} 个重复文件夹"
            if failed_count > 0:
                status += f", {failed_count} 个添加失败"
            self.status_label.setText(status)
        elif skipped_count > 0:
            self.status_label.setText(f"已跳过 {skipped_count} 个重复文件夹")
        elif failed_count > 0:
            self.status_label.setText(f"添加失败: {failed_count} 个文件夹")
            
        # 恢复排序状态
        self.table.setSortingEnabled(was_sorting_enabled)
        
        # 生成预览
        self.generate_previews()
        
        event.acceptProposedAction()

    def _add_folder_contents(self, folder_path: str):
        """递归添加子文件夹到表格"""
        # 创建已存在文件夹路径的规范化集合
        existing_paths = set()
        for path in self.all_files_list:
            existing_paths.add(os.path.normpath(os.path.abspath(path)))
        
        added_count = 0
        skipped_count = 0
        
        try:
            for dirpath, dirnames, _ in os.walk(folder_path):
                for dirname in dirnames:
                    folder_full_path = os.path.join(dirpath, dirname)
                    
                    # 规范化路径以确保一致的比较
                    norm_path = os.path.normpath(os.path.abspath(folder_full_path))
                    
                    if norm_path not in existing_paths:
                        self.all_files_list.append(folder_full_path)  # 保存原始路径格式
                        existing_paths.add(norm_path)  # 添加到规范化集合中
                        
                        if self._add_to_table(folder_full_path):
                            added_count += 1
                        else:
                            # 添加失败时从列表中移除
                            self.all_files_list.remove(folder_full_path)
                            existing_paths.remove(norm_path)
                    else:
                        skipped_count += 1
                        
            return added_count, skipped_count
        except PermissionError as e:
            self.status_label.setText(f"权限错误: {str(e)}")
            return added_count, skipped_count
        except Exception as e:
            self.status_label.setText(f"读取文件夹失败: {str(e)}")
            return added_count, skipped_count